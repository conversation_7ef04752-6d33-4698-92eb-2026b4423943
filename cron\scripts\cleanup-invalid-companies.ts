// Cleanup invalid companies and their associated jobs
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

/**
 * Check if a domain exists using DNS resolution (much faster than HTTP)
 */
async function checkDomainExists(domain: string): Promise<boolean> {
  try {
    const dns = await import("dns");
    const { promisify } = await import("util");
    const lookup = promisify(dns.lookup);

    // Try DNS lookup - this is much faster than HTTP requests
    await lookup(domain);
    return true;
  } catch (error) {
    // If DNS lookup fails, try with www prefix
    try {
      const dns = await import("dns");
      const { promisify } = await import("util");
      const lookup = promisify(dns.lookup);

      await lookup(`www.${domain}`);
      return true;
    } catch (wwwError) {
      return false;
    }
  }
}

/**
 * Extract domain from website URL
 */
function extractDomain(website: string): string | null {
  try {
    const url = new URL(
      website.startsWith("http") ? website : `https://${website}`
    );
    return url.hostname.replace(/^www\./, "");
  } catch (error) {
    return null;
  }
}

async function cleanupInvalidCompanies() {
  logger.info("🧹 Starting cleanup of invalid companies and jobs");

  try {
    // Get all companies with websites
    const companies = await prisma.company.findMany({
      where: {
        website: { not: null },
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
        _count: {
          select: {
            jobListings: true,
          },
        },
      },
    });

    logger.info(
      `📊 Found ${companies.length} companies with websites to check`
    );

    if (companies.length === 0) {
      logger.info("✅ No companies to check");
      return;
    }

    let invalidCompanies: any[] = [];
    let validCompanies = 0;
    let errorCount = 0;

    for (const company of companies) {
      try {
        logger.info(`🔍 Checking: ${company.name}`);

        // Try to get domain from company.domain first, then extract from website
        let domain = company.domain;
        if (!domain && company.website) {
          domain = extractDomain(company.website);
        }

        if (!domain) {
          logger.info(
            `   ❌ No valid domain for ${company.name} - marking for removal`
          );
          invalidCompanies.push({
            ...company,
            reason: "No valid domain",
          });
          continue;
        }

        logger.info(`   🌐 Checking domain: ${domain}`);

        // Check if the domain actually exists
        const domainExists = await checkDomainExists(domain);

        if (!domainExists) {
          logger.info(
            `   ❌ Domain ${domain} does not exist - marking for removal`
          );
          invalidCompanies.push({
            ...company,
            domain,
            reason: "Domain does not exist",
          });
        } else {
          logger.info(`   ✅ Domain ${domain} exists`);
          validCompanies++;
        }

        // Small delay since DNS lookups are fast
        await new Promise((resolve) => setTimeout(resolve, 50));
      } catch (error) {
        logger.error(`❌ Error checking ${company.name}:`, error);
        errorCount++;
      }
    }

    // Summary of findings
    logger.info("\n📊 CLEANUP ANALYSIS RESULTS:");
    logger.info(`   ✅ Valid companies: ${validCompanies}`);
    logger.info(`   ❌ Invalid companies: ${invalidCompanies.length}`);
    logger.info(`   ⚠️ Errors: ${errorCount}`);

    if (invalidCompanies.length === 0) {
      logger.info("🎉 No invalid companies found - database is clean!");
      return;
    }

    // Show invalid companies
    logger.info(`\n❌ INVALID COMPANIES TO BE REMOVED:`);
    let totalJobsToRemove = 0;

    invalidCompanies.forEach((company) => {
      logger.info(`   - ${company.name} (${company.reason})`);
      logger.info(`     Domain: ${company.domain || "N/A"}`);
      logger.info(`     Website: ${company.website}`);
      logger.info(`     Jobs: ${company._count.jobListings}`);
      totalJobsToRemove += company._count.jobListings;
    });

    logger.info(`\n📊 CLEANUP IMPACT:`);
    logger.info(`   🏢 Companies to remove: ${invalidCompanies.length}`);
    logger.info(`   💼 Jobs to remove: ${totalJobsToRemove}`);

    // Ask for confirmation (in production, you might want to make this automatic)
    logger.info(`\n⚠️  PROCEEDING WITH CLEANUP IN 5 SECONDS...`);
    logger.info(
      `   This will permanently delete ${invalidCompanies.length} companies and ${totalJobsToRemove} jobs`
    );

    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Perform cleanup
    logger.info(`\n🗑️  Starting cleanup process...`);

    let companiesRemoved = 0;
    let jobsRemoved = 0;

    for (const company of invalidCompanies) {
      try {
        logger.info(`   🗑️  Removing company: ${company.name}`);

        // First, delete all job listings for this company
        const deletedJobs = await prisma.jobListing.deleteMany({
          where: { companyId: company.id },
        });

        jobsRemoved += deletedJobs.count;
        logger.info(`     ✅ Removed ${deletedJobs.count} jobs`);

        // Then delete the company
        await prisma.company.delete({
          where: { id: company.id },
        });

        companiesRemoved++;
        logger.info(`     ✅ Removed company`);
      } catch (error) {
        logger.error(`     ❌ Error removing ${company.name}:`, error);
        errorCount++;
      }
    }

    // Final summary
    logger.info("\n🎉 CLEANUP COMPLETED!");
    logger.info(`   ✅ Companies removed: ${companiesRemoved}`);
    logger.info(`   ✅ Jobs removed: ${jobsRemoved}`);
    logger.info(`   ❌ Errors: ${errorCount}`);
    logger.info(`   📊 Database cleaned successfully!`);

    if (companiesRemoved > 0) {
      logger.info(`\n📈 Database optimization:`);
      logger.info(`   - Removed ${companiesRemoved} invalid companies`);
      logger.info(`   - Freed up space from ${jobsRemoved} orphaned jobs`);
      logger.info(`   - Improved data quality and accuracy`);
    }
  } catch (error) {
    logger.error("❌ Error during cleanup:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
cleanupInvalidCompanies()
  .then(() => {
    logger.info("✅ Company cleanup script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Company cleanup script failed:", error);
    process.exit(1);
  });

export { cleanupInvalidCompanies };
