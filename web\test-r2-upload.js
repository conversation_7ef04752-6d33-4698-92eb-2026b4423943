#!/usr/bin/env node

/**
 * Test R2 upload with the TLS handshake fix
 * Run this from the web directory where AWS SDK is available
 */

import { S3Client, ListBucketsCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import https from 'https';
import { SSL_OP_NO_SSLv2, SSL_OP_NO_SSLv3 } from 'constants';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const color =
    level === 'SUCCESS'
      ? colors.green
      : level === 'ERROR'
        ? colors.red
        : level === 'WARN'
          ? colors.yellow
          : colors.blue;
  console.log(`${color}[${timestamp}] ${level}:${colors.reset} ${message}`, ...args);
}

// R2 Configuration with TLS handshake fix (same as updated utilities)
const R2_CONFIG = {
  endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
  region: 'auto',
  credentials: {
    accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
    secretAccessKey: '****************************************************************',
  },
  forcePathStyle: true,
  requestHandler: {
    httpsAgent: new https.Agent({
      keepAlive: true,
      rejectUnauthorized: true,
      minVersion: 'TLSv1.2',
      maxVersion: 'TLSv1.3',
      // Let Node.js pick default cipher suites for better compatibility
      secureOptions: SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3,
    }),
  },
};

async function testR2Upload() {
  log('INFO', `${colors.bold}🧪 Testing R2 Upload with TLS Fix${colors.reset}`);

  try {
    const client = new S3Client(R2_CONFIG);

    // Test 1: List buckets
    log('INFO', 'Step 1: Testing ListBuckets operation...');
    const listCommand = new ListBucketsCommand({});
    const listResult = await client.send(listCommand);

    log('SUCCESS', `✅ ListBuckets successful! Found ${listResult.Buckets?.length || 0} buckets:`);
    listResult.Buckets?.forEach((bucket) => {
      log('INFO', `  - ${bucket.Name} (created: ${bucket.CreationDate})`);
    });

    // Test 2: Upload a test file
    log('INFO', 'Step 2: Testing file upload...');
    const testContent = `TLS handshake fix test
Timestamp: ${new Date().toISOString()}
Node.js version: ${process.version}
Platform: ${process.platform}
TLS configuration: minVersion=TLSv1.2, maxVersion=TLSv1.3
Cipher suites: ECDHE-RSA-AES128-GCM-SHA256 and others
Test successful!`;

    const uploadCommand = new PutObjectCommand({
      Bucket: 'hirli-resume-files',
      Key: `tls-fix-test-${Date.now()}.txt`,
      Body: testContent,
      ContentType: 'text/plain',
      Metadata: {
        testType: 'tls-handshake-fix',
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
      },
    });

    const uploadResult = await client.send(uploadCommand);
    log('SUCCESS', '✅ File upload successful!');
    log('INFO', `ETag: ${uploadResult.ETag}`);

    // Test 3: Test with resume bucket specifically
    log('INFO', 'Step 3: Testing resume-specific upload...');
    const resumeUploadCommand = new PutObjectCommand({
      Bucket: 'hirli-resume-files',
      Key: `profile-test-resume-${Date.now()}.pdf`,
      Body: '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF',
      ContentType: 'application/pdf',
      Metadata: {
        originalName: 'test-resume.pdf',
        uploadedAt: new Date().toISOString(),
        fileType: 'resumes',
        bucketType: 'resumes',
      },
    });

    await client.send(resumeUploadCommand);
    log('SUCCESS', '✅ Resume upload successful!');

    return {
      success: true,
      bucketsFound: listResult.Buckets?.length || 0,
      testsCompleted: 3,
    };
  } catch (error) {
    log('ERROR', `❌ R2 upload test failed:`, error.message);

    // Provide specific guidance based on error type
    if (error.message.includes('handshake failure') || error.message.includes('0A000410')) {
      log('ERROR', '🔧 TLS handshake still failing. The cipher configuration may need adjustment.');
      log('INFO', '💡 Try removing the ciphers array to let Node.js pick defaults.');
    } else if (error.message.includes('ENOTFOUND')) {
      log('ERROR', '🌐 DNS resolution failed. Check network connectivity.');
    } else if (
      error.message.includes('credentials') ||
      error.message.includes('SignatureDoesNotMatch')
    ) {
      log('ERROR', '🔑 Credential error. Verify R2 access keys are correct.');
    } else if (error.message.includes('NoSuchBucket')) {
      log('ERROR', '🪣 Bucket not found. Check bucket names and permissions.');
    } else {
      log('ERROR', '❓ Unexpected error type.');
    }

    return {
      success: false,
      error: error.message,
    };
  }
}

async function runTest() {
  log('INFO', `${colors.bold}🚀 Starting R2 Upload Test with TLS Fix${colors.reset}`);
  log('INFO', `Node.js version: ${process.version}`);
  log('INFO', `Platform: ${process.platform}`);

  const result = await testR2Upload();

  // Summary
  log('INFO', `\n${colors.bold}📊 TEST SUMMARY${colors.reset}`);

  if (result.success) {
    log('SUCCESS', `\n🎉 ${colors.bold}R2 UPLOAD TEST PASSED!${colors.reset}`);
    log('INFO', `✅ All ${result.testsCompleted} tests completed successfully`);
    log('INFO', `✅ Found ${result.bucketsFound} R2 buckets`);
    log('INFO', '✅ TLS handshake working properly');
    log('INFO', '✅ File uploads working');
    log('INFO', '\n🚀 Your R2 uploads should now work in production!');
  } else {
    log('ERROR', `\n💥 ${colors.bold}R2 UPLOAD TEST FAILED${colors.reset}`);
    log('ERROR', `❌ Error: ${result.error}`);
    log('INFO', '\n🔧 Additional troubleshooting may be required.');
  }

  return result;
}

// Run the test
runTest().catch(console.error);
