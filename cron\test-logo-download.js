#!/usr/bin/env node

/**
 * Test logo download with known working domains
 */

import { uploadFile } from './lib/storage/r2Storage.js';

async function testLogoDownload() {
  console.log('🧪 Testing logo download with known domains');
  
  // Test domains that definitely exist and likely have logos
  const testDomains = [
    'google.com',
    'microsoft.com',
    'github.com',
    'stripe.com',
    'netflix.com'
  ];
  
  for (const domain of testDomains) {
    console.log(`\n🔍 Testing ${domain}...`);
    
    // Method 1: Try Clearbit
    try {
      const clearbitUrl = `https://logo.clearbit.com/${domain}`;
      console.log(`   📥 Trying Clearbit: ${clearbitUrl}`);
      
      const response = await fetch(clearbitUrl);
      if (response.ok) {
        console.log(`   ✅ Clearbit logo found for ${domain}`);
        
        const buffer = Buffer.from(await response.arrayBuffer());
        const contentType = response.headers.get('content-type') || 'image/png';
        const extension = contentType.includes('svg') ? 'svg' : 'png';
        const filename = `test-${domain.replace('.', '-')}.${extension}`;
        
        console.log(`   📤 Uploading to R2: ${filename}`);
        
        const uploadResult = await uploadFile(
          buffer,
          filename,
          contentType,
          'companyLogos'
        );
        
        if (uploadResult.success) {
          console.log(`   ✅ Successfully uploaded: ${uploadResult.publicUrl}`);
          return { success: true, domain, method: 'clearbit', url: uploadResult.publicUrl };
        }
      } else {
        console.log(`   ❌ Clearbit failed for ${domain} (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ Clearbit error for ${domain}: ${error.message}`);
    }
    
    // Method 2: Try website direct
    const logoUrls = [
      `https://${domain}/logo.png`,
      `https://${domain}/logo.svg`,
      `https://www.${domain}/logo.png`,
      `https://www.${domain}/logo.svg`
    ];
    
    for (const logoUrl of logoUrls) {
      try {
        console.log(`   📥 Trying website: ${logoUrl}`);
        
        const response = await fetch(logoUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        
        if (response.ok && response.headers.get('content-type')?.startsWith('image/')) {
          console.log(`   ✅ Website logo found for ${domain}`);
          
          const buffer = Buffer.from(await response.arrayBuffer());
          const contentType = response.headers.get('content-type') || 'image/png';
          const extension = contentType.includes('svg') ? 'svg' : 'png';
          const filename = `test-website-${domain.replace('.', '-')}.${extension}`;
          
          console.log(`   📤 Uploading to R2: ${filename}`);
          
          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            'companyLogos'
          );
          
          if (uploadResult.success) {
            console.log(`   ✅ Successfully uploaded: ${uploadResult.publicUrl}`);
            return { success: true, domain, method: 'website', url: uploadResult.publicUrl };
          }
        }
      } catch (error) {
        // Continue to next URL
        continue;
      }
    }
    
    console.log(`   ❌ No logo found for ${domain}`);
  }
  
  return { success: false };
}

testLogoDownload().then(result => {
  if (result.success) {
    console.log('\n🎉 Logo download test PASSED!');
    console.log(`✅ Successfully processed: ${result.domain}`);
    console.log(`📋 Method used: ${result.method}`);
    console.log(`🔗 Logo URL: ${result.url}`);
    console.log('✅ The logo script should work for real companies');
  } else {
    console.log('\n💥 Logo download test FAILED!');
    console.log('❌ Could not download any logos');
  }
}).catch(console.error);
