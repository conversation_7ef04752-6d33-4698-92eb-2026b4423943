#!/usr/bin/env node

/**
 * Test Clearbit logo download and R2 upload with known working domains
 */

// Import using require since this is a test file
const { uploadFile } = require("./lib/storage/r2Storage.ts");

async function testClearbitUpload() {
  console.log("🧪 Testing Clearbit logo download and R2 upload");

  // Test with known domains that have Clearbit logos
  const testDomains = [
    "google.com",
    "microsoft.com",
    "github.com",
    "stripe.com",
    "slack.com",
  ];

  for (const domain of testDomains) {
    try {
      console.log(`\n🔍 Testing ${domain}...`);

      const logoUrl = `https://logo.clearbit.com/${domain}`;
      console.log(`   📥 Downloading from: ${logoUrl}`);

      const response = await fetch(logoUrl);
      if (!response.ok) {
        console.log(`   ❌ Logo not available (${response.status})`);
        continue;
      }

      console.log(`   ✅ Downloaded successfully (${response.status})`);

      // Get the image buffer
      const buffer = Buffer.from(await response.arrayBuffer());
      const contentType = response.headers.get("content-type") || "image/png";

      console.log(`   📊 Size: ${buffer.length} bytes, Type: ${contentType}`);

      // Generate filename
      const extension = contentType.includes("svg") ? "svg" : "png";
      const filename = `test-${domain.replace(".", "-")}.${extension}`;

      console.log(`   📤 Uploading to R2: ${filename}`);

      // Upload to R2
      const uploadResult = await uploadFile(
        buffer,
        filename,
        contentType,
        "companyLogos"
      );

      if (uploadResult.success) {
        console.log(`   ✅ Upload successful!`);
        console.log(`   🔗 Public URL: ${uploadResult.publicUrl}`);
        return { success: true, domain, url: uploadResult.publicUrl };
      } else {
        console.log(`   ❌ Upload failed: ${uploadResult.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  return { success: false };
}

testClearbitUpload()
  .then((result) => {
    if (result.success) {
      console.log("\n🎉 Clearbit logo download and R2 upload test PASSED!");
      console.log(`✅ Successfully processed: ${result.domain}`);
      console.log(`🔗 Logo URL: ${result.url}`);
      console.log(
        "✅ The Clearbit script should work for companies with available logos"
      );
    } else {
      console.log("\n💥 Clearbit logo download and R2 upload test FAILED!");
      console.log("❌ Need to investigate the upload process");
    }
  })
  .catch(console.error);
