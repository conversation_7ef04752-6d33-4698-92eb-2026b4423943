#!/usr/bin/env node

/**
 * Minimal R2 test with different configurations
 */

import { S3Client, ListBucketsCommand } from '@aws-sdk/client-s3';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const color = level === 'SUCCESS' ? colors.green : 
                level === 'ERROR' ? colors.red : 
                level === 'WARN' ? colors.yellow : colors.blue;
  console.log(`${color}[${timestamp}] ${level}:${colors.reset} ${message}`, ...args);
}

const credentials = {
  accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
  secretAccessKey: '****************************************************************',
};

const endpoints = [
  'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
  'https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com'
];

async function testConfiguration(name, config) {
  try {
    log('INFO', `Testing ${name}...`);
    const client = new S3Client(config);
    const command = new ListBucketsCommand({});
    const result = await client.send(command);
    log('SUCCESS', `✅ ${name} - Success! Found ${result.Buckets?.length || 0} buckets`);
    return { success: true, buckets: result.Buckets?.length || 0 };
  } catch (error) {
    log('ERROR', `❌ ${name} - Failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  log('INFO', `${colors.bold}🧪 Testing Minimal R2 Configurations${colors.reset}`);
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const endpointName = endpoint.includes('46a6f782') ? 'Primary Endpoint' : 'Alternative Endpoint';
    
    log('INFO', `\n${colors.bold}Testing ${endpointName}: ${endpoint}${colors.reset}`);
    
    // Test 1: Minimal configuration (no custom agent)
    const minimalConfig = {
      endpoint,
      region: 'auto',
      credentials,
      forcePathStyle: true
    };
    
    const minimalResult = await testConfiguration('Minimal Config (no custom agent)', minimalConfig);
    results.push({ endpoint, config: 'minimal', ...minimalResult });
    
    // Test 2: With default HTTPS agent
    const defaultAgentConfig = {
      endpoint,
      region: 'auto',
      credentials,
      forcePathStyle: true,
      requestHandler: {
        httpsAgent: new (await import('https')).default.Agent({
          keepAlive: true,
          rejectUnauthorized: true
        })
      }
    };
    
    const defaultAgentResult = await testConfiguration('Default HTTPS Agent', defaultAgentConfig);
    results.push({ endpoint, config: 'default-agent', ...defaultAgentResult });
    
    // Test 3: With relaxed SSL (for debugging only)
    const relaxedConfig = {
      endpoint,
      region: 'auto',
      credentials,
      forcePathStyle: true,
      requestHandler: {
        httpsAgent: new (await import('https')).default.Agent({
          rejectUnauthorized: false
        })
      }
    };
    
    const relaxedResult = await testConfiguration('Relaxed SSL (DEBUG ONLY)', relaxedConfig);
    results.push({ endpoint, config: 'relaxed-ssl', ...relaxedResult });
    
    // If any configuration worked, we can stop testing this endpoint
    if (minimalResult.success || defaultAgentResult.success) {
      log('SUCCESS', `✅ Found working configuration for ${endpointName}`);
      break;
    }
  }
  
  // Summary
  log('INFO', `\n${colors.bold}📊 TEST SUMMARY${colors.reset}`);
  
  const workingConfigs = results.filter(r => r.success);
  
  if (workingConfigs.length > 0) {
    log('SUCCESS', `\n🎉 ${colors.bold}WORKING CONFIGURATIONS FOUND!${colors.reset}`);
    workingConfigs.forEach(config => {
      log('SUCCESS', `✅ ${config.endpoint} with ${config.config} - ${config.buckets} buckets`);
    });
    
    // Provide recommendation
    const bestConfig = workingConfigs.find(c => c.config === 'minimal') || workingConfigs[0];
    log('INFO', `\n💡 ${colors.bold}RECOMMENDATION:${colors.reset}`);
    log('INFO', `Use endpoint: ${bestConfig.endpoint}`);
    log('INFO', `Use configuration: ${bestConfig.config}`);
    
    if (bestConfig.config === 'minimal') {
      log('INFO', 'No custom HTTPS agent needed - use default AWS SDK configuration');
    } else if (bestConfig.config === 'default-agent') {
      log('INFO', 'Use basic HTTPS agent with keepAlive and rejectUnauthorized: true');
    } else if (bestConfig.config === 'relaxed-ssl') {
      log('WARN', '⚠️  Relaxed SSL worked - this indicates a certificate trust issue');
      log('WARN', '⚠️  DO NOT use relaxed SSL in production');
    }
    
  } else {
    log('ERROR', `\n💥 ${colors.bold}NO WORKING CONFIGURATIONS FOUND${colors.reset}`);
    log('ERROR', 'This indicates a fundamental connectivity or authentication issue');
    
    // Check if relaxed SSL worked
    const relaxedWorked = results.some(r => r.config === 'relaxed-ssl' && r.success);
    if (relaxedWorked) {
      log('WARN', '⚠️  Relaxed SSL worked - certificate trust issue detected');
      log('INFO', '🔧 Try updating Node.js or system certificate store');
    } else {
      log('ERROR', '🔧 Check network connectivity, firewall, and credentials');
    }
  }
  
  return results;
}

// Run the tests
runTests().catch(console.error);
