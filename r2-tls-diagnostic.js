#!/usr/bin/env node

/**
 * Comprehensive R2 TLS Diagnostic Script
 * 
 * This script systematically tests TLS connectivity to Cloudflare R2
 * to identify the exact cause of SSL handshake failures.
 */

import https from 'https';
import { S3Client, ListBucketsCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { spawn } from 'child_process';
import { promisify } from 'util';
import net from 'net';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const color = level === 'SUCCESS' ? colors.green : 
                level === 'ERROR' ? colors.red : 
                level === 'WARN' ? colors.yellow : colors.blue;
  console.log(`${color}[${timestamp}] ${level}:${colors.reset} ${message}`, ...args);
}

// R2 Configuration from your memories
const R2_CREDENTIALS = {
  accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
  secretAccessKey: '****************************************************************'
};

const R2_ENDPOINTS = [
  'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
  'https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com'
];

// Test 1: Basic TCP connectivity
async function testTcpConnectivity(hostname, port = 443) {
  return new Promise((resolve) => {
    log('INFO', `Testing TCP connectivity to ${hostname}:${port}`);
    
    const socket = net.createConnection(port, hostname);
    const timeout = setTimeout(() => {
      socket.destroy();
      resolve({ success: false, error: 'Connection timeout' });
    }, 10000);

    socket.on('connect', () => {
      clearTimeout(timeout);
      socket.destroy();
      log('SUCCESS', `✅ TCP connection to ${hostname}:${port} successful`);
      resolve({ success: true });
    });

    socket.on('error', (error) => {
      clearTimeout(timeout);
      log('ERROR', `❌ TCP connection to ${hostname}:${port} failed:`, error.message);
      resolve({ success: false, error: error.message });
    });
  });
}

// Test 2: OpenSSL certificate verification
async function testOpenSslCertificate(hostname) {
  return new Promise((resolve) => {
    log('INFO', `Testing SSL certificate for ${hostname} using OpenSSL`);
    
    const openssl = spawn('openssl', ['s_client', '-showcerts', '-connect', `${hostname}:443`], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    openssl.stdout.on('data', (data) => {
      output += data.toString();
    });

    openssl.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    openssl.on('close', (code) => {
      if (output.includes('Verify return code: 0 (ok)')) {
        log('SUCCESS', `✅ SSL certificate for ${hostname} is valid`);
        resolve({ success: true, output });
      } else {
        log('ERROR', `❌ SSL certificate verification failed for ${hostname}`);
        log('ERROR', 'OpenSSL output:', output.substring(0, 500));
        resolve({ success: false, error: errorOutput, output });
      }
    });

    openssl.on('error', (error) => {
      log('WARN', `OpenSSL not available: ${error.message}`);
      resolve({ success: false, error: 'OpenSSL not available' });
    });

    // Send EOF to close the connection
    setTimeout(() => {
      openssl.stdin.end();
    }, 3000);
  });
}

// Test 3: Node.js HTTPS request
async function testNodeHttpsRequest(url) {
  return new Promise((resolve) => {
    log('INFO', `Testing Node.js HTTPS request to ${url}`);
    
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: 443,
      path: '/',
      method: 'GET',
      rejectUnauthorized: true,
      timeout: 10000
    };

    const req = https.request(options, (res) => {
      log('SUCCESS', `✅ HTTPS request successful. Status: ${res.statusCode}`);
      resolve({ success: true, statusCode: res.statusCode });
    });

    req.on('error', (error) => {
      log('ERROR', `❌ HTTPS request failed:`, error.message);
      resolve({ success: false, error: error.message });
    });

    req.on('timeout', () => {
      req.destroy();
      log('ERROR', `❌ HTTPS request timed out`);
      resolve({ success: false, error: 'Request timeout' });
    });

    req.end();
  });
}

// Test 4: AWS SDK S3Client with different configurations
async function testAwsSdkConfigurations(endpoint) {
  const configurations = [
    {
      name: 'Default Configuration',
      config: {
        endpoint,
        region: 'auto',
        credentials: R2_CREDENTIALS
      }
    },
    {
      name: 'With forcePathStyle',
      config: {
        endpoint,
        region: 'auto',
        credentials: R2_CREDENTIALS,
        forcePathStyle: true
      }
    },
    {
      name: 'With Custom HTTPS Agent (TLS 1.2)',
      config: {
        endpoint,
        region: 'auto',
        credentials: R2_CREDENTIALS,
        forcePathStyle: true,
        requestHandler: {
          httpsAgent: new https.Agent({
            keepAlive: true,
            rejectUnauthorized: true,
            secureProtocol: 'TLSv1_2_method'
          })
        }
      }
    },
    {
      name: 'With Relaxed SSL (TESTING ONLY)',
      config: {
        endpoint,
        region: 'auto',
        credentials: R2_CREDENTIALS,
        forcePathStyle: true,
        requestHandler: {
          httpsAgent: new https.Agent({
            rejectUnauthorized: false
          })
        }
      }
    }
  ];

  for (const { name, config } of configurations) {
    try {
      log('INFO', `Testing AWS SDK with: ${name}`);
      
      const client = new S3Client(config);
      const command = new ListBucketsCommand({});
      
      const result = await client.send(command);
      log('SUCCESS', `✅ ${name} - ListBuckets successful. Found ${result.Buckets?.length || 0} buckets`);
      
      return { success: true, config: name, buckets: result.Buckets?.length || 0 };
    } catch (error) {
      log('ERROR', `❌ ${name} failed:`, error.message);
      
      // If this is the relaxed SSL test and it works, we know it's a certificate issue
      if (name.includes('Relaxed SSL') && !error.message.includes('handshake')) {
        log('WARN', '⚠️  Relaxed SSL worked - this indicates a certificate trust issue');
      }
    }
  }
  
  return { success: false };
}

// Test 5: Simple upload test
async function testSimpleUpload(endpoint) {
  try {
    log('INFO', `Testing simple file upload to ${endpoint}`);
    
    const client = new S3Client({
      endpoint,
      region: 'auto',
      credentials: R2_CREDENTIALS,
      forcePathStyle: true,
      requestHandler: {
        httpsAgent: new https.Agent({
          keepAlive: true,
          rejectUnauthorized: true,
          secureProtocol: 'TLSv1_2_method'
        })
      }
    });

    const command = new PutObjectCommand({
      Bucket: 'hirli-resume-files',
      Key: 'test-diagnostic.txt',
      Body: 'TLS diagnostic test file',
      ContentType: 'text/plain'
    });

    await client.send(command);
    log('SUCCESS', `✅ File upload test successful`);
    return { success: true };
  } catch (error) {
    log('ERROR', `❌ File upload test failed:`, error.message);
    return { success: false, error: error.message };
  }
}

// Main diagnostic function
async function runDiagnostics() {
  log('INFO', `${colors.bold}🔍 Starting R2 TLS Diagnostics${colors.reset}`);
  log('INFO', `Node.js version: ${process.version}`);
  log('INFO', `Platform: ${process.platform}`);
  
  const results = {
    tcp: {},
    ssl: {},
    https: {},
    awsSdk: {},
    upload: {}
  };

  // Test each endpoint
  for (const endpoint of R2_ENDPOINTS) {
    const hostname = new URL(endpoint).hostname;
    log('INFO', `\n${colors.bold}Testing endpoint: ${endpoint}${colors.reset}`);
    
    // Test 1: TCP connectivity
    results.tcp[endpoint] = await testTcpConnectivity(hostname);
    
    // Test 2: SSL certificate (if TCP works)
    if (results.tcp[endpoint].success) {
      results.ssl[endpoint] = await testOpenSslCertificate(hostname);
    }
    
    // Test 3: Node.js HTTPS
    results.https[endpoint] = await testNodeHttpsRequest(endpoint);
    
    // Test 4: AWS SDK configurations
    results.awsSdk[endpoint] = await testAwsSdkConfigurations(endpoint);
    
    // Test 5: Upload test (if basic connectivity works)
    if (results.https[endpoint].success) {
      results.upload[endpoint] = await testSimpleUpload(endpoint);
    }
  }

  // Summary
  log('INFO', `\n${colors.bold}📊 DIAGNOSTIC SUMMARY${colors.reset}`);
  
  for (const endpoint of R2_ENDPOINTS) {
    log('INFO', `\nEndpoint: ${endpoint}`);
    log('INFO', `  TCP: ${results.tcp[endpoint]?.success ? '✅' : '❌'}`);
    log('INFO', `  SSL: ${results.ssl[endpoint]?.success ? '✅' : '❌'}`);
    log('INFO', `  HTTPS: ${results.https[endpoint]?.success ? '✅' : '❌'}`);
    log('INFO', `  AWS SDK: ${results.awsSdk[endpoint]?.success ? '✅' : '❌'}`);
    log('INFO', `  Upload: ${results.upload[endpoint]?.success ? '✅' : '❌'}`);
  }

  return results;
}

// Run diagnostics
runDiagnostics().catch(console.error);
