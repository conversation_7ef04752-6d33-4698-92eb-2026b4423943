// Simple R2 connection test with different SSL configurations
import { S3Client, ListBucketsCommand } from "@aws-sdk/client-s3";
import https from "https";

// Test different configurations
const configs = [
  {
    name: "Working Endpoint - Minimal Config",
    config: {
      endpoint:
        "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com", // Working endpoint
      region: "auto",
      credentials: {
        accessKeyId: "c3a71f217fdf3a056efaefab3a17afc5",
        secretAccessKey:
          "****************************************************************",
      },
      forcePathStyle: true,
    },
  },
  {
    name: "Old Endpoint (Should Fail)",
    config: {
      endpoint:
        "https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com", // Problematic endpoint
      region: "auto",
      credentials: {
        accessKeyId: "c3a71f217fdf3a056efaefab3a17afc5",
        secretAccessKey:
          "****************************************************************",
      },
      forcePathStyle: true,
    },
  },
  {
    name: "Alternative Endpoint Format",
    config: {
      endpoint:
        "https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com",
      region: "weur",
      credentials: {
        accessKeyId: "c3a71f217fdf3a056efaefab3a17afc5",
        secretAccessKey:
          "****************************************************************",
      },
      forcePathStyle: true,
    },
  },
];

async function testConfigs() {
  console.log("🧪 Testing different R2 configurations...\n");

  for (const { name, config } of configs) {
    console.log(`📋 Testing: ${name}`);

    try {
      const client = new S3Client(config);
      const command = new ListBucketsCommand({});

      console.log("   🔄 Attempting connection...");
      const response = await client.send(command);

      console.log(
        `   ✅ Success! Found ${response.Buckets?.length || 0} buckets`
      );
      if (response.Buckets && response.Buckets.length > 0) {
        console.log(
          "   📦 Buckets:",
          response.Buckets.map((b) => b.Name).join(", ")
        );
      }

      // If this config works, we can stop here
      console.log(`   🎉 ${name} works! Using this configuration.\n`);
      break;
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   🔍 Error code: ${error.code || "Unknown"}`);
      console.log("");
    }
  }
}

testConfigs();
