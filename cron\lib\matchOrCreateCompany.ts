import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

/**
 * Check if a domain exists and is accessible (DNS + HTTP check)
 */
async function checkDomainExists(domain: string): Promise<boolean> {
  try {
    // Step 1: Check DNS first (fast)
    const dns = await import("dns");
    const { promisify } = await import("util");
    const lookup = promisify(dns.lookup);

    let dnsExists = false;
    try {
      await lookup(domain);
      dnsExists = true;
    } catch (error) {
      // Try with www prefix
      try {
        await lookup(`www.${domain}`);
        dnsExists = true;
      } catch (wwwError) {
        return false; // DNS doesn't exist at all
      }
    }

    if (!dnsExists) {
      return false;
    }

    // Step 2: Check if website is actually accessible (HTTP check)
    const urlsToTest = [
      `https://${domain}`,
      `https://www.${domain}`,
      `http://${domain}`,
      `http://www.${domain}`,
    ];

    for (const url of urlsToTest) {
      try {
        const response = await fetch(url, {
          method: "HEAD",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          },
          timeout: 8000, // 8 second timeout
        });

        // If we get any reasonable response (even 404), the site exists
        if (response.status < 500) {
          return true;
        }
      } catch (error) {
        // Continue to next URL
        continue;
      }
    }

    // DNS exists but no HTTP response - domain is not functional
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Extract domain from website URL or apply link
 */
function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url.startsWith("http") ? url : `https://${url}`);
    return urlObj.hostname.replace(/^www\./, "");
  } catch (error) {
    return null;
  }
}

/**
 * Extract potential company domain from job data
 */
function extractCompanyDomain(
  companyName: string,
  jobData?: {
    applyLink?: string;
    description?: string;
  }
): string | null {
  // Method 1: Extract from apply link
  if (jobData?.applyLink) {
    const domain = extractDomain(jobData.applyLink);
    if (domain) {
      // Skip common job board domains
      const jobBoardDomains = [
        "indeed.com",
        "linkedin.com",
        "glassdoor.com",
        "monster.com",
        "ziprecruiter.com",
        "careerbuilder.com",
        "simplyhired.com",
        "jobs.com",
        "workday.com",
        "greenhouse.io",
        "lever.co",
        "smartrecruiters.com",
        "icims.com",
        "jobvite.com",
        "bing.com",
        "google.com",
      ];

      if (!jobBoardDomains.some((jbDomain) => domain.includes(jbDomain))) {
        return domain;
      }
    }
  }

  // Method 2: Generate domain from company name
  const cleanName = companyName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, "")
    .replace(/\s+/g, "")
    .trim();

  if (cleanName.length > 2) {
    return `${cleanName}.com`;
  }

  return null;
}

/**
 * Match or create a company by name
 * @param prisma Prisma client instance to use for database operations
 * @param name Company name to match or create
 * @param jobData Additional job data for domain extraction
 * @param stateId Optional state ID to associate with the company
 * @returns The company ID if found or created, undefined otherwise
 */
export async function matchOrCreateCompany(
  prisma: PrismaClient,
  name: string,
  jobData?: {
    applyLink?: string;
    description?: string;
  },
  stateId?: string
): Promise<string | undefined> {
  if (!name || name.trim() === "" || name.toLowerCase() === "unknown") {
    logger.debug(`🚫 Skipping empty or unknown company name: ${name}`);
    return undefined;
  }

  // Skip city names that are likely not companies
  const cityPrefixes = ["city of", "town of", "village of", "county of"];
  const cityKeywords = [
    "city",
    "town",
    "village",
    "county",
    "borough",
    "township",
    "district",
  ];
  const nameLower = name.toLowerCase();

  // Check for city prefixes
  if (cityPrefixes.some((prefix) => nameLower.startsWith(prefix))) {
    logger.debug(`🏙️ Skipping city name as company: ${name}`);
    return undefined;
  }

  // Check for city names with city keywords
  if (cityKeywords.some((keyword) => nameLower.includes(keyword))) {
    // Additional check to avoid false positives (e.g., "City Bank")
    const commonCompanyWords = [
      "bank",
      "corp",
      "inc",
      "llc",
      "company",
      "group",
      "services",
      "international",
      "hotel",
      "market",
      "agency",
      "entertainment",
      "restaurant",
      "cafe",
      "store",
      "shop",
      "center",
      "institute",
    ];

    // Check if the name contains any common company words
    const isLikelyCompany = commonCompanyWords.some((word) =>
      nameLower.includes(word)
    );

    // Check if the name is a sports team or other organization with a city name
    const sportsTeamKeywords = [
      "team",
      "club",
      "united",
      "fc",
      "sc",
      "athletics",
    ];
    const isSportsTeam = sportsTeamKeywords.some((word) =>
      nameLower.includes(word)
    );

    // Check for specific patterns that indicate a company rather than a city
    // For example: "San Francisco 49ers", "New York Life Insurance"
    const hasNumbersOrSpecialChars =
      /[0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(nameLower);

    // If it's likely a company, sports team, or has special patterns, don't filter it out
    if (isLikelyCompany || isSportsTeam || hasNumbersOrSpecialChars) {
      // It's likely a legitimate company with a city keyword, so don't filter it
      return null; // Continue with company matching
    } else {
      logger.debug(`🏙️ Skipping potential city name as company: ${name}`);
      return undefined;
    }
  }

  // Check if the name is just a city name (e.g., "Beverly Hills")
  try {
    // Check for exact city name match
    const cityExists = await prisma.city.findFirst({
      where: {
        name: {
          equals: name,
          mode: "insensitive",
        },
      },
    });

    if (cityExists) {
      logger.debug(`🏙️ Skipping exact city name match as company: ${name}`);
      return undefined;
    }

    // Check for city name with state code (e.g., "Beverly Hills, CA")
    const cityWithStateRegex = /^(.+),\s*([A-Z]{2})$/;
    const match = name.match(cityWithStateRegex);

    if (match) {
      const cityName = match[1].trim();
      const stateCode = match[2];

      const cityWithState = await prisma.city.findFirst({
        where: {
          name: {
            equals: cityName,
            mode: "insensitive",
          },
          state: {
            code: stateCode,
          },
        },
      });

      if (cityWithState) {
        logger.debug(`🏙️ Skipping city with state match as company: ${name}`);
        return undefined;
      }
    }
  } catch (err) {
    // If there's an error checking the city, continue with company matching
    logger.warn(`⚠️ Error checking if name is a city: ${name}`, err);
  }

  try {
    // Step 1: Try to find an existing company with the exact name
    let company = await prisma.company.findUnique({ where: { name } });

    if (company) {
      // Check if this existing company is actually a city
      const cityExists = await prisma.city.findFirst({
        where: {
          name: {
            equals: name,
            mode: "insensitive",
          },
        },
      });

      if (cityExists) {
        logger.debug(
          `🏙️ Found existing company that is a city: ${name} (${company.id}) - skipping`
        );
        return undefined;
      }

      // Check for city with state code (e.g., "Beverly Hills, CA")
      const cityWithStateRegex = /^(.+),\s*([A-Z]{2})$/;
      const match = name.match(cityWithStateRegex);

      if (match) {
        const cityName = match[1].trim();
        const stateCode = match[2];

        const cityWithState = await prisma.city.findFirst({
          where: {
            name: {
              equals: cityName,
              mode: "insensitive",
            },
            state: {
              code: stateCode,
            },
          },
        });

        if (cityWithState) {
          logger.debug(
            `🏙️ Found existing company that is a city with state: ${name} (${company.id}) - skipping`
          );
          return undefined;
        }
      }

      // Company exists in our cleaned database - it's legitimate
      logger.debug(
        `✅ Found existing validated company: ${name} (${company.id})`
      );
      return company.id;
    }

    // Step 2: Company doesn't exist - check if we should mark it as invalid
    // First check if this company was previously marked as invalid
    const invalidCompany = await prisma.company.findFirst({
      where: {
        name,
        domain: null, // Companies with null domain were likely marked as invalid
        website: null,
      },
    });

    if (invalidCompany) {
      logger.debug(
        `🚫 Found previously invalidated company: ${name} - skipping`
      );
      return undefined;
    }

    // Step 3: New company - validate domain before creating
    logger.debug(`🔍 NEW COMPANY: Validating domain for: ${name}`);

    // Extract potential domain from job data or company name
    const potentialDomain = extractCompanyDomain(name, jobData);

    if (potentialDomain) {
      logger.debug(`🌐 Checking domain: ${potentialDomain}`);

      // Check if the domain actually exists (DNS + HTTP validation)
      const domainExists = await checkDomainExists(potentialDomain);

      if (!domainExists) {
        logger.warn(
          `❌ FAKE COMPANY: Domain ${potentialDomain} does not exist`
        );
        logger.warn(
          `🚫 Skipping company creation for: ${name} (invalid domain) - job will be skipped`
        );
        return undefined; // Return undefined to indicate fake company
      }

      logger.debug(
        `✅ VALID COMPANY: Domain ${potentialDomain} exists - creating company`
      );
    } else {
      logger.debug(
        `⚠️ Could not extract domain for company: ${name} - proceeding without validation`
      );
    }

    // Create a new company (domain validation passed or no domain to check)
    const companyData: any = {
      name,
      createdAt: new Date(),
    };

    // If we found a valid domain, store it
    if (potentialDomain) {
      companyData.domain = potentialDomain;
      companyData.website = `https://${potentialDomain}`;
      logger.debug(
        `🌐 Setting domain and website for new company: ${name} (${potentialDomain})`
      );
    }

    // If stateId is provided, set it as the headquartersStateId
    if (stateId) {
      companyData.headquartersStateId = stateId;
      logger.debug(
        `🏢 Setting headquartersStateId for new company: ${name} (${stateId})`
      );
    }

    try {
      company = await prisma.company.create({
        data: companyData,
      });

      logger.debug(`🏢 Created new company: ${name} (${company.id})`);
      return company.id;
    } catch (error: any) {
      // Check if this is a unique constraint error (company with this name already exists)
      if (error.code === "P2002" && error.meta?.target?.includes("name")) {
        // Try to find the company again (it might have been created by another process)
        const existingCompany = await prisma.company.findUnique({
          where: { name },
        });
        if (existingCompany) {
          logger.debug(
            `🏢 Found existing company after creation attempt: ${name} (${existingCompany.id})`
          );
          return existingCompany.id;
        }
      }

      // Re-throw the error if it's not a unique constraint error or if we couldn't find the company
      throw error;
    }
  } catch (err) {
    logger.warn(`⚠️ Failed to match or create company: ${name}`, err);
    return undefined;
  }
}
