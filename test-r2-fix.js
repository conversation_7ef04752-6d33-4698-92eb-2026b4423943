#!/usr/bin/env node

/**
 * Test R2 connection with the TLS handshake fix
 */

import { S3Client, ListBucketsCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import https from 'https';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const color = level === 'SUCCESS' ? colors.green : 
                level === 'ERROR' ? colors.red : 
                level === 'WARN' ? colors.yellow : colors.blue;
  console.log(`${color}[${timestamp}] ${level}:${colors.reset} ${message}`, ...args);
}

// R2 Configuration with TLS handshake fix (same as updated web utility)
const R2_CONFIG = {
  endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
  region: 'auto',
  credentials: {
    accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
    secretAccessKey: '****************************************************************',
  },
  forcePathStyle: true,
  requestHandler: {
    httpsAgent: new https.Agent({
      keepAlive: true,
      rejectUnauthorized: true,
      // Use default TLS version negotiation instead of forcing specific version
      minVersion: 'TLSv1.2',
      maxVersion: 'TLSv1.3',
      // Enable specific cipher suites that work with Cloudflare R2
      ciphers: [
        'ECDHE-RSA-AES128-GCM-SHA256',
        'ECDHE-RSA-AES256-GCM-SHA384',
        'ECDHE-RSA-CHACHA20-POLY1305',
        'ECDHE-ECDSA-AES128-GCM-SHA256',
        'ECDHE-ECDSA-AES256-GCM-SHA384',
        'ECDHE-ECDSA-CHACHA20-POLY1305'
      ].join(':'),
      // Disable legacy protocols that might cause handshake issues
      secureOptions: require('constants').SSL_OP_NO_SSLv2 | require('constants').SSL_OP_NO_SSLv3,
    }),
  },
};

async function testR2Connection() {
  log('INFO', `${colors.bold}🧪 Testing R2 Connection with TLS Fix${colors.reset}`);
  
  try {
    // Test 1: List buckets
    log('INFO', 'Testing ListBuckets operation...');
    const client = new S3Client(R2_CONFIG);
    const listCommand = new ListBucketsCommand({});
    const listResult = await client.send(listCommand);
    
    log('SUCCESS', `✅ ListBuckets successful! Found ${listResult.Buckets?.length || 0} buckets:`);
    listResult.Buckets?.forEach(bucket => {
      log('INFO', `  - ${bucket.Name} (created: ${bucket.CreationDate})`);
    });
    
    // Test 2: Upload a small test file
    log('INFO', 'Testing file upload...');
    const uploadCommand = new PutObjectCommand({
      Bucket: 'hirli-resume-files',
      Key: 'tls-fix-test.txt',
      Body: `TLS fix test - ${new Date().toISOString()}`,
      ContentType: 'text/plain',
      Metadata: {
        testType: 'tls-handshake-fix',
        timestamp: new Date().toISOString()
      }
    });
    
    await client.send(uploadCommand);
    log('SUCCESS', '✅ File upload successful!');
    
    // Test 3: Verify the public URL format
    const publicUrl = `https://pub-46a6f782171b440493a823a520764a72.r2.dev/tls-fix-test.txt`;
    log('INFO', `📄 File should be accessible at: ${publicUrl}`);
    
    return {
      success: true,
      bucketsFound: listResult.Buckets?.length || 0,
      uploadSuccessful: true
    };
    
  } catch (error) {
    log('ERROR', `❌ R2 connection test failed:`, error.message);
    
    // Provide specific guidance based on error type
    if (error.message.includes('handshake failure')) {
      log('ERROR', '🔧 TLS handshake still failing. This may require additional cipher configuration.');
    } else if (error.message.includes('ENOTFOUND')) {
      log('ERROR', '🌐 DNS resolution failed. Check network connectivity.');
    } else if (error.message.includes('credentials')) {
      log('ERROR', '🔑 Credential error. Verify R2 access keys.');
    } else {
      log('ERROR', '❓ Unexpected error type.');
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

async function testAlternativeEndpoint() {
  log('INFO', `${colors.bold}🔄 Testing Alternative R2 Endpoint${colors.reset}`);
  
  const alternativeConfig = {
    ...R2_CONFIG,
    endpoint: 'https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com'
  };
  
  try {
    const client = new S3Client(alternativeConfig);
    const listCommand = new ListBucketsCommand({});
    const listResult = await client.send(listCommand);
    
    log('SUCCESS', `✅ Alternative endpoint works! Found ${listResult.Buckets?.length || 0} buckets`);
    return { success: true, endpoint: alternativeConfig.endpoint };
  } catch (error) {
    log('ERROR', `❌ Alternative endpoint failed:`, error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  log('INFO', `${colors.bold}🚀 Starting R2 TLS Fix Verification${colors.reset}`);
  
  // Test primary endpoint
  const primaryResult = await testR2Connection();
  
  // If primary fails, test alternative
  let alternativeResult = null;
  if (!primaryResult.success) {
    alternativeResult = await testAlternativeEndpoint();
  }
  
  // Summary
  log('INFO', `\n${colors.bold}📊 TEST SUMMARY${colors.reset}`);
  log('INFO', `Primary endpoint: ${primaryResult.success ? '✅ Working' : '❌ Failed'}`);
  
  if (alternativeResult) {
    log('INFO', `Alternative endpoint: ${alternativeResult.success ? '✅ Working' : '❌ Failed'}`);
  }
  
  if (primaryResult.success || alternativeResult?.success) {
    log('SUCCESS', `\n🎉 ${colors.bold}R2 CONNECTION FIXED!${colors.reset}`);
    log('INFO', 'Your R2 uploads should now work properly.');
    
    if (alternativeResult?.success && !primaryResult.success) {
      log('INFO', `💡 Consider updating your R2_ENDPOINT to: ${alternativeResult.endpoint}`);
    }
  } else {
    log('ERROR', `\n💥 ${colors.bold}R2 CONNECTION STILL FAILING${colors.reset}`);
    log('INFO', 'Additional troubleshooting may be required.');
  }
  
  return {
    primary: primaryResult,
    alternative: alternativeResult
  };
}

// Run the tests
runTests().catch(console.error);
