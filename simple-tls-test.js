#!/usr/bin/env node

/**
 * Simple TLS Diagnostic Script for R2
 * Tests basic connectivity without external dependencies
 */

import https from "https";
import net from "net";
import { spawn } from "child_process";

// Colors for console output
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
  bold: "\x1b[1m",
};

function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const color =
    level === "SUCCESS"
      ? colors.green
      : level === "ERROR"
      ? colors.red
      : level === "WARN"
      ? colors.yellow
      : colors.blue;
  console.log(
    `${color}[${timestamp}] ${level}:${colors.reset} ${message}`,
    ...args
  );
}

const R2_ENDPOINTS = [
  "https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com",
  "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
];

// Test 1: Basic TCP connectivity
async function testTcpConnectivity(hostname, port = 443) {
  return new Promise((resolve) => {
    log("INFO", `Testing TCP connectivity to ${hostname}:${port}`);

    const socket = net.createConnection(port, hostname);
    const timeout = setTimeout(() => {
      socket.destroy();
      resolve({ success: false, error: "Connection timeout" });
    }, 10000);

    socket.on("connect", () => {
      clearTimeout(timeout);
      socket.destroy();
      log("SUCCESS", `✅ TCP connection to ${hostname}:${port} successful`);
      resolve({ success: true });
    });

    socket.on("error", (error) => {
      clearTimeout(timeout);
      log(
        "ERROR",
        `❌ TCP connection to ${hostname}:${port} failed:`,
        error.message
      );
      resolve({ success: false, error: error.message });
    });
  });
}

// Test 2: Node.js HTTPS request with different TLS configurations
async function testHttpsWithConfigs(url) {
  const configs = [
    {
      name: "Default HTTPS",
      options: {
        rejectUnauthorized: true,
      },
    },
    {
      name: "TLS 1.2 Only",
      options: {
        rejectUnauthorized: true,
        secureProtocol: "TLSv1_2_method",
      },
    },
    {
      name: "TLS 1.3 Only",
      options: {
        rejectUnauthorized: true,
        secureProtocol: "TLSv1_3_method",
      },
    },
    {
      name: "Relaxed SSL (TEST ONLY)",
      options: {
        rejectUnauthorized: false,
      },
    },
  ];

  const urlObj = new URL(url);

  for (const config of configs) {
    const result = await new Promise((resolve) => {
      log("INFO", `Testing ${config.name} for ${url}`);

      const options = {
        hostname: urlObj.hostname,
        port: 443,
        path: "/",
        method: "GET",
        timeout: 10000,
        ...config.options,
      };

      const req = https.request(options, (res) => {
        log(
          "SUCCESS",
          `✅ ${config.name} successful. Status: ${
            res.statusCode
          }, TLS: ${res.socket?.getProtocol?.()}`
        );
        resolve({
          success: true,
          statusCode: res.statusCode,
          protocol: res.socket?.getProtocol?.(),
        });
      });

      req.on("error", (error) => {
        log("ERROR", `❌ ${config.name} failed:`, error.message);
        resolve({ success: false, error: error.message });
      });

      req.on("timeout", () => {
        req.destroy();
        log("ERROR", `❌ ${config.name} timed out`);
        resolve({ success: false, error: "Request timeout" });
      });

      req.end();
    });

    if (result.success) {
      return { success: true, workingConfig: config.name, ...result };
    }
  }

  return { success: false };
}

// Test 3: OpenSSL certificate check
async function testOpenSslCertificate(hostname) {
  return new Promise((resolve) => {
    log("INFO", `Testing SSL certificate for ${hostname} using OpenSSL`);

    const openssl = spawn(
      "openssl",
      [
        "s_client",
        "-showcerts",
        "-connect",
        `${hostname}:443`,
        "-servername",
        hostname,
      ],
      {
        stdio: ["pipe", "pipe", "pipe"],
      }
    );

    let output = "";
    let errorOutput = "";

    openssl.stdout.on("data", (data) => {
      output += data.toString();
    });

    openssl.stderr.on("data", (data) => {
      errorOutput += data.toString();
    });

    openssl.on("close", (code) => {
      if (output.includes("Verify return code: 0 (ok)")) {
        log("SUCCESS", `✅ SSL certificate for ${hostname} is valid`);

        // Extract certificate details
        const certMatch = output.match(/subject=(.+)/);
        const issuerMatch = output.match(/issuer=(.+)/);
        const protocolMatch = output.match(/Protocol\s*:\s*(.+)/);

        resolve({
          success: true,
          subject: certMatch?.[1],
          issuer: issuerMatch?.[1],
          protocol: protocolMatch?.[1],
        });
      } else {
        log("ERROR", `❌ SSL certificate verification failed for ${hostname}`);
        if (output.includes("verify error")) {
          const verifyError = output.match(/verify error:(.+)/)?.[1];
          log("ERROR", `Verification error: ${verifyError}`);
        }
        resolve({
          success: false,
          error: errorOutput,
          output: output.substring(0, 500),
        });
      }
    });

    openssl.on("error", (error) => {
      log("WARN", `OpenSSL not available: ${error.message}`);
      resolve({ success: false, error: "OpenSSL not available" });
    });

    // Send EOF to close the connection
    setTimeout(() => {
      openssl.stdin.end();
    }, 5000);
  });
}

// Test 4: Check Node.js version and TLS support
async function checkNodeTlsSupport() {
  log("INFO", `Node.js version: ${process.version}`);
  log("INFO", `Platform: ${process.platform} ${process.arch}`);

  try {
    const tls = await import("tls");
    const supportedVersions = tls.getCiphers ? "Available" : "Limited";
    log("INFO", `TLS support: ${supportedVersions}`);

    if (process.env.NODE_TLS_REJECT_UNAUTHORIZED) {
      log(
        "WARN",
        `NODE_TLS_REJECT_UNAUTHORIZED is set to: ${process.env.NODE_TLS_REJECT_UNAUTHORIZED}`
      );
    }
  } catch (error) {
    log("ERROR", `TLS module error: ${error.message}`);
  }
}

// Main diagnostic function
async function runDiagnostics() {
  log(
    "INFO",
    `${colors.bold}🔍 Starting Simple R2 TLS Diagnostics${colors.reset}`
  );

  await checkNodeTlsSupport();

  const results = {};

  // Test each endpoint
  for (const endpoint of R2_ENDPOINTS) {
    const hostname = new URL(endpoint).hostname;
    log("INFO", `\n${colors.bold}Testing endpoint: ${endpoint}${colors.reset}`);

    results[endpoint] = {};

    // Test 1: TCP connectivity
    results[endpoint].tcp = await testTcpConnectivity(hostname);

    // Test 2: SSL certificate (if TCP works)
    if (results[endpoint].tcp.success) {
      results[endpoint].ssl = await testOpenSslCertificate(hostname);
    }

    // Test 3: HTTPS with different configurations
    results[endpoint].https = await testHttpsWithConfigs(endpoint);
  }

  // Summary
  log("INFO", `\n${colors.bold}📊 DIAGNOSTIC SUMMARY${colors.reset}`);

  for (const endpoint of R2_ENDPOINTS) {
    const hostname = new URL(endpoint).hostname;
    log("INFO", `\nEndpoint: ${endpoint}`);
    log(
      "INFO",
      `  TCP: ${results[endpoint].tcp?.success ? "✅" : "❌"} ${
        results[endpoint].tcp?.error || ""
      }`
    );
    log(
      "INFO",
      `  SSL: ${results[endpoint].ssl?.success ? "✅" : "❌"} ${
        results[endpoint].ssl?.error || ""
      }`
    );
    log(
      "INFO",
      `  HTTPS: ${results[endpoint].https?.success ? "✅" : "❌"} ${
        results[endpoint].https?.workingConfig ||
        results[endpoint].https?.error ||
        ""
      }`
    );

    if (results[endpoint].ssl?.success) {
      log("INFO", `  Certificate: ${results[endpoint].ssl.subject}`);
      log("INFO", `  Issuer: ${results[endpoint].ssl.issuer}`);
    }
  }

  // Recommendations
  log("INFO", `\n${colors.bold}💡 RECOMMENDATIONS${colors.reset}`);

  const workingEndpoints = R2_ENDPOINTS.filter(
    (endpoint) => results[endpoint].https?.success
  );

  if (workingEndpoints.length > 0) {
    log("SUCCESS", `✅ Working endpoints found: ${workingEndpoints.length}`);
    log("INFO", `Use these endpoints in your R2 configuration:`);
    workingEndpoints.forEach((endpoint) => {
      const config = results[endpoint].https.workingConfig;
      log("INFO", `  - ${endpoint} (${config})`);
    });
  } else {
    log(
      "ERROR",
      `❌ No working endpoints found. This indicates a network or certificate issue.`
    );

    const tcpWorking = R2_ENDPOINTS.filter(
      (endpoint) => results[endpoint].tcp?.success
    );
    if (tcpWorking.length === 0) {
      log(
        "ERROR",
        `Network connectivity issue - check firewall/proxy settings`
      );
    } else {
      log(
        "ERROR",
        `TLS/SSL issue - check certificate trust store or Node.js version`
      );
    }
  }

  return results;
}

// Run diagnostics
runDiagnostics().catch(console.error);
