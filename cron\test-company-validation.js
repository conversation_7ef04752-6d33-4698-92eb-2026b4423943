#!/usr/bin/env node

/**
 * Test the optimized company validation flow
 */

import { matchOrCreateCompany } from './lib/matchOrCreateCompany.js';
import { getPrismaClient } from './utils/prismaClient.js';

async function testCompanyValidation() {
  console.log('🧪 Testing optimized company validation flow');
  
  const prisma = await getPrismaClient('web');
  
  // Test cases
  const testCases = [
    {
      name: 'Microsoft', // Should exist in cleaned DB
      jobData: { applyLink: 'https://careers.microsoft.com/job123' },
      expected: 'existing_company'
    },
    {
      name: 'Google', // Should exist in cleaned DB  
      jobData: { applyLink: 'https://careers.google.com/job456' },
      expected: 'existing_company'
    },
    {
      name: 'FakeCompany123', // Should be validated and rejected
      jobData: { applyLink: 'https://fakecompany123.com/jobs' },
      expected: 'fake_company'
    },
    {
      name: 'TestValidCompany', // Should be validated and accepted
      jobData: { applyLink: 'https://github.com/jobs' }, // Use real domain
      expected: 'new_valid_company'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`   Expected: ${testCase.expected}`);
    
    try {
      const startTime = Date.now();
      const result = await matchOrCreateCompany(
        prisma,
        testCase.name,
        testCase.jobData
      );
      const duration = Date.now() - startTime;
      
      if (result) {
        console.log(`   ✅ Result: Company created/found (ID: ${result})`);
        console.log(`   ⏱️  Duration: ${duration}ms`);
        
        if (testCase.expected === 'existing_company') {
          console.log(`   🎯 PASS: Existing company found quickly`);
        } else if (testCase.expected === 'new_valid_company') {
          console.log(`   🎯 PASS: New valid company created`);
        } else {
          console.log(`   ❌ UNEXPECTED: Expected fake company but got valid result`);
        }
      } else {
        console.log(`   🚫 Result: Company rejected (fake/invalid)`);
        console.log(`   ⏱️  Duration: ${duration}ms`);
        
        if (testCase.expected === 'fake_company') {
          console.log(`   🎯 PASS: Fake company correctly rejected`);
        } else {
          console.log(`   ❌ UNEXPECTED: Expected valid company but got rejection`);
        }
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
    }
  }
  
  console.log('\n📊 Test Summary:');
  console.log('✅ Existing companies should be found quickly (no domain validation)');
  console.log('🚫 Fake companies should be rejected after domain validation');
  console.log('🆕 New valid companies should be created with domain info');
  console.log('⚡ Performance: Existing companies = fast, New companies = slower (validation)');
  
  await prisma.$disconnect();
}

testCompanyValidation().catch(console.error);
