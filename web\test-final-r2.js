#!/usr/bin/env node

/**
 * Final test of the R2 upload fix
 */

import { S3Client, ListBucketsCommand, PutObjectCommand } from '@aws-sdk/client-s3';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message, ...args) {
  const timestamp = new Date().toISOString();
  const color = level === 'SUCCESS' ? colors.green : 
                level === 'ERROR' ? colors.red : 
                level === 'WARN' ? colors.yellow : colors.blue;
  console.log(`${color}[${timestamp}] ${level}:${colors.reset} ${message}`, ...args);
}

// R2 Configuration - Using the working endpoint (same as updated utilities)
const R2_CONFIG = {
  endpoint: 'https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com', // Working endpoint
  region: 'auto',
  credentials: {
    accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
    secretAccessKey: '****************************************************************',
  },
  forcePathStyle: true, // Required for R2 compatibility
};

async function testFinalR2Upload() {
  log('INFO', `${colors.bold}🎯 Final R2 Upload Test${colors.reset}`);
  
  try {
    const client = new S3Client(R2_CONFIG);
    
    // Test 1: List buckets
    log('INFO', 'Step 1: Testing ListBuckets...');
    const listCommand = new ListBucketsCommand({});
    const listResult = await client.send(listCommand);
    
    log('SUCCESS', `✅ ListBuckets successful! Found ${listResult.Buckets?.length || 0} buckets:`);
    listResult.Buckets?.forEach(bucket => {
      log('INFO', `  - ${bucket.Name} (created: ${bucket.CreationDate})`);
    });
    
    // Test 2: Upload a test resume file
    log('INFO', 'Step 2: Testing resume upload...');
    const resumeContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test Resume - TLS Fix Successful!) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000074 00000 n 
0000000120 00000 n 
0000000179 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
273
%%EOF`;

    const resumeUploadCommand = new PutObjectCommand({
      Bucket: 'hirli-resume-files',
      Key: `profile-test-resume-${Date.now()}.pdf`,
      Body: resumeContent,
      ContentType: 'application/pdf',
      Metadata: {
        originalName: 'test-resume-tls-fix.pdf',
        uploadedAt: new Date().toISOString(),
        fileType: 'resumes',
        bucketType: 'resumes',
        testType: 'final-tls-fix-verification'
      }
    });
    
    const uploadResult = await client.send(resumeUploadCommand);
    log('SUCCESS', '✅ Resume upload successful!');
    log('INFO', `ETag: ${uploadResult.ETag}`);
    
    // Test 3: Upload a company logo test
    log('INFO', 'Step 3: Testing company logo upload...');
    const logoUploadCommand = new PutObjectCommand({
      Bucket: 'hirli-company-logos',
      Key: `test-logo-${Date.now()}.txt`,
      Body: 'Test company logo content - TLS fix successful!',
      ContentType: 'text/plain',
      Metadata: {
        originalName: 'test-logo.txt',
        uploadedAt: new Date().toISOString(),
        fileType: 'companyLogos',
        bucketType: 'company'
      }
    });
    
    await client.send(logoUploadCommand);
    log('SUCCESS', '✅ Company logo upload successful!');
    
    return {
      success: true,
      bucketsFound: listResult.Buckets?.length || 0,
      testsCompleted: 3
    };
    
  } catch (error) {
    log('ERROR', `❌ Final R2 test failed:`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

async function runFinalTest() {
  log('INFO', `${colors.bold}🚀 Running Final R2 TLS Fix Verification${colors.reset}`);
  log('INFO', `Node.js version: ${process.version}`);
  log('INFO', `Platform: ${process.platform}`);
  log('INFO', `Endpoint: https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com`);
  log('INFO', `Configuration: Minimal (no custom HTTPS agent)`);
  
  const result = await testFinalR2Upload();
  
  // Summary
  log('INFO', `\n${colors.bold}📊 FINAL TEST SUMMARY${colors.reset}`);
  
  if (result.success) {
    log('SUCCESS', `\n🎉 ${colors.bold}R2 TLS FIX SUCCESSFUL!${colors.reset}`);
    log('SUCCESS', `✅ All ${result.testsCompleted} tests passed`);
    log('SUCCESS', `✅ Found ${result.bucketsFound} R2 buckets`);
    log('SUCCESS', '✅ Resume uploads working');
    log('SUCCESS', '✅ Company logo uploads working');
    log('SUCCESS', '✅ TLS handshake issues resolved');
    
    log('INFO', `\n${colors.bold}🔧 IMPLEMENTATION SUMMARY:${colors.reset}`);
    log('INFO', '✅ Updated web/src/lib/utils/r2DocumentUpload.ts');
    log('INFO', '✅ Updated cron/lib/storage/r2Storage.ts');
    log('INFO', '✅ Changed endpoint to working URL');
    log('INFO', '✅ Removed complex TLS configuration');
    log('INFO', '✅ Using minimal AWS SDK configuration');
    
    log('INFO', `\n${colors.bold}🚀 NEXT STEPS:${colors.reset}`);
    log('INFO', '1. Test your actual upload functionality in the web app');
    log('INFO', '2. Verify cron jobs can upload files successfully');
    log('INFO', '3. Update environment variables if needed');
    log('INFO', '4. Monitor for any remaining issues');
    
  } else {
    log('ERROR', `\n💥 ${colors.bold}FINAL TEST FAILED${colors.reset}`);
    log('ERROR', `❌ Error: ${result.error}`);
    log('INFO', '\n🔧 Additional investigation required.');
  }
  
  return result;
}

// Run the final test
runFinalTest().catch(console.error);
