// Add company logos directly from company websites - Upload to R2 storage
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { uploadFile, BucketType } from "../lib/storage/r2Storage";

const prisma = new PrismaClient();

/**
 * Download Clearbit logo and upload to R2 storage
 */
async function downloadAndUploadClearbitLogo(
  domain: string,
  companyName: string
): Promise<string | null> {
  try {
    const logoUrl = `https://logo.clearbit.com/${domain}`;
    logger.info(`   📥 Downloading logo from: ${logoUrl}`);

    const response = await fetch(logoUrl);
    if (!response.ok) {
      logger.info(
        `   ❌ Logo not available for ${domain} (${response.status})`
      );
      return null;
    }

    // Get the image buffer
    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get("content-type") || "image/png";

    // Generate a clean filename
    const cleanCompanyName = companyName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

    const extension = contentType.includes("svg") ? "svg" : "png";
    const filename = `${cleanCompanyName}-${domain}.${extension}`;

    logger.info(`   📤 Uploading to R2: ${filename}`);

    // Upload to R2 using our storage utility
    const uploadResult = await uploadFile(
      buffer,
      filename,
      contentType,
      "companyLogos"
    );

    if (uploadResult.success) {
      logger.info(`   ✅ Uploaded successfully: ${uploadResult.publicUrl}`);
      return uploadResult.publicUrl!;
    } else {
      logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
      return null;
    }
  } catch (error) {
    logger.error(
      `   ❌ Error downloading/uploading logo for ${domain}:`,
      error
    );
    return null;
  }
}

/**
 * Try to download logo directly from company website
 */
async function tryDownloadFromWebsite(
  domain: string,
  companyName: string
): Promise<string | null> {
  try {
    // Common logo paths to try
    const logoUrls = [
      `https://${domain}/logo.png`,
      `https://${domain}/logo.svg`,
      `https://${domain}/assets/logo.png`,
      `https://${domain}/assets/logo.svg`,
      `https://${domain}/images/logo.png`,
      `https://${domain}/images/logo.svg`,
      `https://${domain}/static/logo.png`,
      `https://${domain}/static/logo.svg`,
      `https://${domain}/img/logo.png`,
      `https://${domain}/img/logo.svg`,
      `https://www.${domain}/logo.png`,
      `https://www.${domain}/logo.svg`,
      `https://www.${domain}/assets/logo.png`,
      `https://www.${domain}/assets/logo.svg`,
      `https://www.${domain}/images/logo.png`,
      `https://www.${domain}/images/logo.svg`,
    ];

    logger.info(`   🔍 Trying company website: ${domain}`);

    for (const logoUrl of logoUrls) {
      try {
        const response = await fetch(logoUrl, {
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
          },
        });

        if (
          response.ok &&
          response.headers.get("content-type")?.startsWith("image/")
        ) {
          logger.info(`   ✅ Found logo at: ${logoUrl}`);

          // Get the image buffer
          const buffer = Buffer.from(await response.arrayBuffer());
          const contentType =
            response.headers.get("content-type") || "image/png";

          // Generate a clean filename
          const cleanCompanyName = companyName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, "-")
            .replace(/-+/g, "-")
            .replace(/^-|-$/g, "");

          const extension = contentType.includes("svg")
            ? "svg"
            : contentType.includes("jpeg")
              ? "jpg"
              : "png";
          const filename = `${cleanCompanyName}-${domain.replace(/\./g, "-")}.${extension}`;

          logger.info(`   📤 Uploading website logo to R2: ${filename}`);

          // Upload to R2 using our storage utility
          const uploadResult = await uploadFile(
            buffer,
            filename,
            contentType,
            "companyLogos"
          );

          if (uploadResult.success) {
            logger.info(
              `   ✅ Website logo uploaded successfully: ${uploadResult.publicUrl}`
            );
            return uploadResult.publicUrl!;
          } else {
            logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
            return null;
          }
        }
      } catch (urlError) {
        // Continue to next URL silently
        continue;
      }
    }

    logger.info(`   ❌ No logo found on company website: ${domain}`);
    return null;
  } catch (error) {
    logger.error(`   ❌ Error searching company website for ${domain}:`, error);
    return null;
  }
}

/**
 * Extract domain from website URL
 */
function extractDomain(website: string): string | null {
  try {
    const url = new URL(
      website.startsWith("http") ? website : `https://${website}`
    );
    return url.hostname.replace(/^www\./, "");
  } catch (error) {
    return null;
  }
}

async function addClearbitLogos() {
  logger.info("🎨 Adding Clearbit logos for companies");

  try {
    // Get companies without logos that have websites
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: null,
        website: { not: null },
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
      },
      take: 10, // Process 10 companies for testing
    });

    logger.info(`📊 Found ${companies.length} companies that need logos`);

    if (companies.length === 0) {
      logger.info("✅ No companies need logos");
      return;
    }

    let logosAdded = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const company of companies) {
      try {
        logger.info(`🔍 Processing: ${company.name}`);

        // Try to get domain from company.domain first, then extract from website
        let domain = company.domain;
        if (!domain && company.website) {
          domain = extractDomain(company.website);
        }

        if (!domain) {
          logger.info(`   ⏭️ No domain available for ${company.name}`);
          skippedCount++;
          continue;
        }

        logger.info(`   🌐 Processing domain: ${domain}`);

        // Try Method 1: Download Clearbit logo and upload to R2
        let r2LogoUrl = await downloadAndUploadClearbitLogo(
          domain,
          company.name
        );

        // Try Method 2: If Clearbit fails, try company website directly
        if (!r2LogoUrl) {
          logger.info(`   🔄 Clearbit failed, trying company website...`);
          r2LogoUrl = await tryDownloadFromWebsite(domain, company.name);
        }

        if (r2LogoUrl) {
          // Update company with R2 logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: r2LogoUrl,
              domain: domain, // Also update domain if it wasn't set
            },
          });

          logger.info(`   ✅ Added R2 logo: ${r2LogoUrl}`);
          logosAdded++;
        } else {
          logger.info(`   ❌ No logo found for ${domain} using any method`);
          skippedCount++;
        }

        // Small delay to be respectful to Clearbit
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        errorCount++;
      }
    }

    // Summary
    logger.info("\n🎉 Company logo processing completed!");
    logger.info(`   ✅ Logos uploaded to R2: ${logosAdded}`);
    logger.info(`   ⏭️ Skipped: ${skippedCount}`);
    logger.info(`   ❌ Errors: ${errorCount}`);
    logger.info(`   📊 Total processed: ${companies.length}`);

    if (logosAdded > 0) {
      logger.info(
        `\n📈 Success rate: ${((logosAdded / companies.length) * 100).toFixed(1)}%`
      );
      logger.info(
        `🔗 All logos are now stored in R2 and accessible via worker URL`
      );
      logger.info(
        `📋 Methods used: Clearbit API + Company website direct download`
      );
    }

    // Show some examples of companies that got logos
    if (logosAdded > 0) {
      logger.info(`\n✅ Companies with new R2 logos:`);
      const companiesWithLogos = await prisma.company.findMany({
        where: {
          logoUrl: { contains: "hirli-static-assets" },
        },
        select: { name: true, logoUrl: true },
        take: 10,
      });

      companiesWithLogos.forEach((company) => {
        logger.info(`   - ${company.name}: ${company.logoUrl}`);
      });
    }
  } catch (error) {
    logger.error("❌ Error adding Clearbit logos:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addClearbitLogos()
  .then(() => {
    logger.info("✅ Clearbit logo script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Clearbit logo script failed:", error);
    process.exit(1);
  });

export { addClearbitLogos };
